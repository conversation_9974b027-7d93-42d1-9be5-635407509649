# Jest Snapshot Testing Guide

This document provides comprehensive information about the Jest snapshot tests implemented for the Accordion and Carousel components in manystack.com project.

## Overview

1. **Accordion Component** (`app/components/Accordion.tsx`)
2. **Carousel Components** (`components/ui/carousel.tsx` and `app/components/carousel-cards/CarouselCard.tsx`)

## Test Coverage

### Accordion Component Tests

**Location**: `__tests__/components/Accordion.test.tsx`

**Test Scenarios**:
- ✅ Empty state (renders null when items array is empty)
- ✅ Single item accordion in vertical orientation (mobile)
- ✅ Single item accordion in horizontal orientation (xl screen)
- ✅ Multiple items accordion in vertical orientation (mobile)
- ✅ Multiple items accordion in horizontal orientation (xl screen)
- ✅ Screen size variations (undefined screen size)
- ✅ Content variations (long text content)
- ✅ Special characters and formatting

**Total Tests**: 9 tests with 9 snapshots

### Carousel Component Tests

**Location**: `__tests__/components/Carousel.test.tsx`

**Test Scenarios**:
- ✅ Basic carousel with horizontal orientation (default)
- ✅ Carousel with custom options
- ✅ Single slide carousel
- ✅ Multiple slides carousel
- ✅ Carousel with navigation controls
- ✅ Carousel with complex content
- ✅ Vertical carousel with navigation
- ✅ Carousel with custom class

**Total Tests**: 9 tests with 9 snapshots

### CarouselCard Component Tests

**Location**: `__tests__/components/CarouselCard.test.tsx`

**Test Scenarios**:
- ✅ Basic rendering with single child
- ✅ Basic rendering with multiple children
- ✅ Carousel card with title
- ✅ Carousel card with title and multiple children
- ✅ Carousel card without title
- ✅ Carousel card with overlay
- ✅ Carousel card with title and overlay
- ✅ Carousel card with custom className
- ✅ Carousel card with custom className and title
- ✅ Navigation visibility (single vs multiple children)
- ✅ Event handlers
- ✅ Complex nested content scenarios

**Total Tests**: 13 tests with 13 snapshots

## Running Tests

### Run All Tests
```bash
npm test
```

### Run Tests in Watch Mode
```bash
npm run test:watch
```

### Run Tests with Coverage
```bash
npm run test:coverage
```

### Run Specific Test File
```bash
npm test __tests__/components/Accordion.test.tsx
npm test __tests__/components/Carousel.test.tsx
npm test __tests__/components/CarouselCard.test.tsx
```
