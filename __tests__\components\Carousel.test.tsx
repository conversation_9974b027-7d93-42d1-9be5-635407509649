import React from 'react'
import { render, fireEvent } from '@testing-library/react'
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from '@/components/ui/carousel'

// Mock embla-carousel-react
const mockScrollNext = jest.fn()
const mockScrollPrev = jest.fn()

jest.mock('embla-carousel-react', () => ({
  __esModule: true,
  default: jest.fn(() => [
    jest.fn(), // carouselRef
    {
      canScrollPrev: jest.fn(() => true),
      canScrollNext: jest.fn(() => true),
      scrollPrev: mockScrollPrev,
      scrollNext: mockScrollNext,
      on: jest.fn(),
      off: jest.fn(),
    },
  ]),
}))

describe('Carousel Components', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks()
  })
  describe('Basic Carousel', () => {
    it('should render carousel with horizontal orientation by default', () => {
      const { container } = render(
        <Carousel>
          <CarouselContent>
            <CarouselItem>
              <div>Slide 1</div>
            </CarouselItem>
          </CarouselContent>
        </Carousel>
      )

      expect(container).toMatchSnapshot()
    })

    it('should render carousel with custom options', () => {
      const { container } = render(
        <Carousel opts={{ loop: true, align: 'center' }}>
          <CarouselContent>
            <CarouselItem>
              <div>Slide 1</div>
            </CarouselItem>
          </CarouselContent>
        </Carousel>
      )

      expect(container).toMatchSnapshot()
    })
  })

  describe('Single slide carousel', () => {
    it('should render carousel with single slide', () => {
      const { container } = render(
        <Carousel>
          <CarouselContent>
            <CarouselItem>
              <div>Single Slide Content</div>
            </CarouselItem>
          </CarouselContent>
        </Carousel>
      )

      expect(container).toMatchSnapshot()
    })
  })

  describe('Multiple slides carousel', () => {
    it('should render carousel with multiple slides', () => {
      const { container } = render(
        <Carousel>
          <CarouselContent>
            <CarouselItem>
              <div>Slide 1</div>
            </CarouselItem>
            <CarouselItem>
              <div>Slide 2</div>
            </CarouselItem>
            <CarouselItem>
              <div>Slide 3</div>
            </CarouselItem>
          </CarouselContent>
        </Carousel>
      )

      expect(container).toMatchSnapshot()
    })

    it('should render carousel with navigation controls', () => {
      const { container } = render(
        <Carousel>
          <CarouselContent>
            <CarouselItem>
              <div>Slide 1</div>
            </CarouselItem>
            <CarouselItem>
              <div>Slide 2</div>
            </CarouselItem>
          </CarouselContent>
          <CarouselPrevious />
          <CarouselNext />
        </Carousel>
      )

      expect(container).toMatchSnapshot()
    })
  })

  describe('Carousel with different content types', () => {
    it('should render carousel with complex content', () => {
      const { container } = render(
        <Carousel>
          <CarouselContent>
            <CarouselItem>
              <div className="p-4">
                <h3>Card Title 1</h3>
                <p>Card description with some text content.</p>
                <button>Action Button</button>
              </div>
            </CarouselItem>
            <CarouselItem>
              <div className="p-4">
                <h3>Card Title 2</h3>
                <p>Another card with different content.</p>
                <button>Another Button</button>
              </div>
            </CarouselItem>
          </CarouselContent>
        </Carousel>
      )

      expect(container).toMatchSnapshot()
    })
  })

  describe('Carousel orientations', () => {
    it('should render vertical carousel with navigation', () => {
      const { container } = render(
        <Carousel orientation="vertical">
          <CarouselContent>
            <CarouselItem>
              <div>Vertical Slide 1</div>
            </CarouselItem>
            <CarouselItem>
              <div>Vertical Slide 2</div>
            </CarouselItem>
          </CarouselContent>
          <CarouselPrevious />
          <CarouselNext />
        </Carousel>
      )

      expect(container).toMatchSnapshot()
    })
  })

  describe('Carousel with custom classes', () => {
    it('should render carousel with custom className', () => {
      const { container } = render(
        <Carousel className="custom-carousel-class">
          <CarouselContent className="custom-content-class">
            <CarouselItem className="custom-item-class">
              <div>Custom Styled Slide</div>
            </CarouselItem>
          </CarouselContent>
        </Carousel>
      )

      expect(container).toMatchSnapshot()
    })
  })

  describe('Carousel navigation functionality', () => {
    it('should call scrollNext when next button is clicked', () => {
      const { getByRole } = render(
        <Carousel>
          <CarouselContent>
            <CarouselItem>
              <div>Slide 1</div>
            </CarouselItem>
            <CarouselItem>
              <div>Slide 2</div>
            </CarouselItem>
            <CarouselItem>
              <div>Slide 3</div>
            </CarouselItem>
          </CarouselContent>
          <CarouselNext />
          <CarouselPrevious />
        </Carousel>
      )

      const nextButton = getByRole('button', { name: /next slide/i })
      fireEvent.click(nextButton)

      expect(mockScrollNext).toHaveBeenCalledTimes(1)
    })

    it('should call scrollPrev when previous button is clicked', () => {
      const { getByRole } = render(
        <Carousel>
          <CarouselContent>
            <CarouselItem>
              <div>Slide 1</div>
            </CarouselItem>
            <CarouselItem>
              <div>Slide 2</div>
            </CarouselItem>
            <CarouselItem>
              <div>Slide 3</div>
            </CarouselItem>
          </CarouselContent>
          <CarouselNext />
          <CarouselPrevious />
        </Carousel>
      )

      const prevButton = getByRole('button', { name: /previous slide/i })
      fireEvent.click(prevButton)

      expect(mockScrollPrev).toHaveBeenCalledTimes(1)
    })

    it('should handle multiple clicks on navigation buttons', () => {
      const { getByRole } = render(
        <Carousel>
          <CarouselContent>
            <CarouselItem>
              <div>Slide 1</div>
            </CarouselItem>
            <CarouselItem>
              <div>Slide 2</div>
            </CarouselItem>
            <CarouselItem>
              <div>Slide 3</div>
            </CarouselItem>
          </CarouselContent>
          <CarouselNext />
          <CarouselPrevious />
        </Carousel>
      )

      const nextButton = getByRole('button', { name: /next slide/i })
      const prevButton = getByRole('button', { name: /previous slide/i })

      // Click next button multiple times
      fireEvent.click(nextButton)
      fireEvent.click(nextButton)
      fireEvent.click(nextButton)

      // Click previous button multiple times
      fireEvent.click(prevButton)
      fireEvent.click(prevButton)

      expect(mockScrollNext).toHaveBeenCalledTimes(3)
      expect(mockScrollPrev).toHaveBeenCalledTimes(2)
    })
  })
})
